# Decorative Graphics for Stiletto HTML Shell

This directory contains decorative graphics that appear directly adjacent to the game viewport edges, creating a frame-like effect around the game area.

## Image Placement Guide

Place your decorative images in this directory with the following names:

### Corner Graphics (120x80px recommended)
- `top-left.png` - Top-left corner decoration (positioned above-left of viewport)
- `top-right.png` - Top-right corner decoration (positioned above-right of viewport)
- `bottom-left.png` - Bottom-left corner decoration (positioned below-left of viewport)
- `bottom-right.png` - Bottom-right corner decoration (positioned below-right of viewport)

### Edge Graphics
- `left-edge.png` - Left side decoration (120px wide, matches viewport height)
- `right-edge.png` - Right side decoration (120px wide, matches viewport height)
- `top-edge.png` - Top center decoration (matches viewport width, 80px tall)
- `bottom-edge.png` - Bottom center decoration (matches viewport width, 80px tall)

## Behavior

- Graphics are positioned directly adjacent to the viewport edges with a 10px gap
- They have `pointer-events: none` so they don't interfere with game interaction
- They automatically hide in fullscreen mode
- They hide on smaller screens (< 1000px width or < 800px height) to avoid viewport interference
- Images use `background-size: contain` to maintain aspect ratio
- Edge graphics align to the appropriate side (left-edge aligns right, right-edge aligns left, etc.)
- Corner graphics align to their respective corners

## Supported Formats

- PNG (recommended for transparency)
- JPG/JPEG
- WebP
- SVG

## Tips

- Use transparent backgrounds (PNG) for best visual integration
- Keep file sizes reasonable for web loading
- Consider the dark theme of the shell when designing graphics
- Graphics should complement, not distract from, the game viewport
