# Decorative Graphics for Stiletto HTML Shell

This directory contains decorative graphics that appear around the game viewport edges.

## Image Placement Guide

Place your decorative images in this directory with the following names:

### Corner Graphics (200x150px recommended)
- `top-left.png` - Top-left corner decoration
- `top-right.png` - Top-right corner decoration  
- `bottom-left.png` - Bottom-left corner decoration
- `bottom-right.png` - Bottom-right corner decoration

### Edge Graphics
- `left-edge.png` - Left side decoration (100x300px recommended)
- `right-edge.png` - Right side decoration (100x300px recommended)
- `top-edge.png` - Top center decoration (300x80px recommended)
- `bottom-edge.png` - Bottom center decoration (300x80px recommended)

## Behavior

- Graphics are positioned absolutely around the viewport
- They have `pointer-events: none` so they don't interfere with game interaction
- They automatically hide in fullscreen mode
- They hide on smaller screens (< 1000px width or < 800px height) to avoid viewport interference
- Images use `background-size: contain` to maintain aspect ratio

## Supported Formats

- PNG (recommended for transparency)
- JPG/JPEG
- WebP
- SVG

## Tips

- Use transparent backgrounds (PNG) for best visual integration
- Keep file sizes reasonable for web loading
- Consider the dark theme of the shell when designing graphics
- Graphics should complement, not distract from, the game viewport
